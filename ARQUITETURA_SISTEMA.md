# Documentação Técnica - Billify Generator BR

## Visão Geral

O **Billify Generator BR** é uma aplicação web moderna para geração de faturas brasileiras, desenvolvida com React e focada na experiência do usuário brasileiro. O sistema oferece múltiplos templates de fatura, integração com PIX, e exportação para PDF.

## 1. Arquitetura do Sistema

### 1.1 Tipo de Arquitetura
- **Arquitetura:** Single Page Application (SPA) - Frontend Only
- **Padrão:** Component-Based Architecture com React
- **Estrutura:** Monolítica frontend com separação clara de responsabilidades

### 1.2 Diagrama da Arquitetura

```mermaid
graph TB
    A[Browser] --> B[React App]
    B --> C[React Router]
    C --> D[Index Page - Formulário]
    C --> E[Template Page - Visualização]
    
    D --> F[Componentes de Abas]
    F --> G[InvoiceDetailsTab]
    F --> H[BusinessClientTab]
    F --> I[ShippingTransportTab]
    F --> J[ItemsTab]
    F --> K[PreviewSummaryTab]
    
    E --> L[Template Registry]
    L --> M[Template 1-10]
    
    B --> N[Utilities]
    N --> O[PDF Generator]
    N --> P[PIX QR Code]
    N --> Q[Currency Formatter]
    N --> R[Calculations]
    
    B --> S[Local Storage]
    S --> T[Form Data Persistence]
```

### 1.3 Fluxo de Dados

1. **Entrada de Dados:** Usuário preenche formulário em abas sequenciais
2. **Persistência:** Dados salvos automaticamente no localStorage
3. **Processamento:** Cálculos realizados em tempo real
4. **Visualização:** Preview do template selecionado
5. **Exportação:** Geração de PDF via html2canvas + jsPDF

### 1.4 Estrutura de Diretórios

```
src/
├── components/           # Componentes React
│   ├── ui/              # Componentes shadcn/ui
│   ├── templates/       # Templates de fatura (1-10)
│   ├── *Tab.jsx         # Componentes de abas do formulário
│   └── *.jsx            # Outros componentes
├── pages/               # Páginas principais
│   ├── Index.jsx        # Página principal do formulário
│   └── TemplatePage.jsx # Página de visualização/PDF
├── utils/               # Utilitários e helpers
│   ├── formatCurrency.js
│   ├── invoiceCalculations.js
│   ├── locale.js
│   ├── pixQRCode.js
│   └── pdfGenerator.js
├── lib/                 # Bibliotecas auxiliares
└── styles/              # Estilos CSS
```

### 1.5 Padrões Arquiteturais

- **Component Composition:** Composição de componentes reutilizáveis
- **Props Drilling:** Passagem de dados via props entre componentes
- **State Lifting:** Estado elevado para componentes pais quando necessário
- **Separation of Concerns:** Separação clara entre UI, lógica de negócio e utilitários

## 2. Interface do Usuário (UI)

### 2.1 Sistema de Abas/Passos

O sistema utiliza um fluxo de 5 abas sequenciais para criação de faturas:

#### Aba 1: Detalhes da Fatura (`InvoiceDetailsTab`)
- **Funcionalidade:** Configuração básica da fatura
- **Campos:** Número da fatura, datas, upload de logo
- **Validações:** Número único, datas válidas
- **Componente:** `src/components/InvoiceDetailsTab.jsx`

#### Aba 2: De/Para (`BusinessClientTab`)
- **Funcionalidade:** Dados da empresa e cliente
- **Campos:** Informações completas de empresa e cliente
- **Modais:** BusinessDetailsModal, ClientDetailsModal
- **Componente:** `src/components/BusinessClientTab.jsx`

#### Aba 3: Envio (`ShippingTransportTab`)
- **Funcionalidade:** Detalhes de envio e transporte
- **Campos:** Endereços de origem/destino, transportadora
- **Componente:** `src/components/ShippingTransportTab.jsx`

#### Aba 4: Itens (`ItemsTab`)
- **Funcionalidade:** Lista de produtos/serviços
- **Campos:** Nome, descrição, quantidade, valor, impostos
- **Cálculos:** Subtotal, impostos, total geral em tempo real
- **Componente:** `src/components/ItemsTab.jsx`

#### Aba 5: Visualizar (`PreviewSummaryTab`)
- **Funcionalidade:** Preview e configurações finais
- **Recursos:** Seleção de template, dados PIX, termos e condições
- **Ações:** Visualização completa, geração de PDF
- **Componente:** `src/components/PreviewSummaryTab.jsx`

### 2.2 Fluxo de Navegação

```mermaid
graph LR
    A[Detalhes da Fatura] --> B[De/Para]
    B --> C[Envio]
    C --> D[Itens]
    D --> E[Visualizar]
    E --> F[Template Page]
    F --> G[PDF Export]
    
    A -.-> B
    B -.-> A
    B -.-> C
    C -.-> B
    C -.-> D
    D -.-> C
    D -.-> E
    E -.-> D
```

### 2.3 Componentes de UI Principais

#### Navegação entre Abas
- **Controles:** Botões "Anterior" e "Próximo"
- **Indicadores:** Círculos coloridos mostrando progresso
- **Estados:** Não iniciado (cinza), em progresso (roxo), concluído (verde)

#### Formulários
- **Inputs:** FloatingLabelInput para melhor UX
- **Validação:** Validação em tempo real
- **Persistência:** Auto-save no localStorage

#### Templates
- **Quantidade:** 10 templates diferentes
- **Seleção:** Grid visual com preview
- **Customização:** Dados dinâmicos em cada template

### 2.4 Design Patterns na Interface

- **Progressive Disclosure:** Informações reveladas progressivamente nas abas
- **Immediate Feedback:** Cálculos e validações em tempo real
- **Consistent Navigation:** Padrão uniforme de navegação
- **Responsive Design:** Layout adaptável via Tailwind CSS

## 3. Tecnologias e Stack Técnico

### 3.1 Frontend

#### Core Technologies
- **React 18.2.0:** Biblioteca principal para UI
- **Vite 5.1.4:** Build tool e dev server
- **React Router DOM 6.23.1:** Roteamento SPA

#### UI Framework & Styling
- **Tailwind CSS 3.4.4:** Framework CSS utility-first
- **shadcn/ui:** Componentes UI modernos e acessíveis
- **Radix UI:** Primitivos de UI headless
- **Lucide React:** Ícones SVG
- **Framer Motion 11.3.9:** Animações

#### Form Management
- **React Hook Form 7.52.0:** Gerenciamento de formulários
- **Zod 3.23.8:** Validação de esquemas
- **@hookform/resolvers 3.6.0:** Integração Zod + RHF

#### Specialized Libraries
- **html2canvas 1.4.1:** Captura de tela para PDF
- **jsPDF 2.5.2:** Geração de PDF
- **qrcode 1.5.4:** Geração de QR Code PIX
- **date-fns 4.1.0:** Manipulação de datas

#### State & Data Management
- **TanStack Query 5.48.0:** Cache e sincronização de dados
- **Local Storage:** Persistência de dados do formulário

### 3.2 Dependências Principais

```json
{
  "react": "^18.2.0",
  "react-dom": "^18.2.0",
  "react-router-dom": "^6.23.1",
  "react-hook-form": "^7.52.0",
  "tailwindcss": "^3.4.4",
  "html2canvas": "^1.4.1",
  "jspdf": "^2.5.2",
  "qrcode": "^1.5.4"
}
```

### 3.3 Ferramentas de Desenvolvimento

- **ESLint:** Linting de código JavaScript/React
- **PostCSS:** Processamento de CSS
- **Autoprefixer:** Prefixos CSS automáticos
- **Vite:** Hot Module Replacement (HMR)

### 3.4 Justificativa das Tecnologias

#### React
- **Motivo:** Ecossistema maduro, componentes reutilizáveis, grande comunidade
- **Benefícios:** Desenvolvimento ágil, manutenibilidade, performance

#### Vite
- **Motivo:** Build rápido, HMR instantâneo, configuração mínima
- **Benefícios:** Experiência de desenvolvimento superior ao Webpack

#### Tailwind CSS
- **Motivo:** Desenvolvimento rápido, consistência visual, bundle otimizado
- **Benefícios:** Menos CSS customizado, design system integrado

#### shadcn/ui
- **Motivo:** Componentes modernos, acessibilidade, customizáveis
- **Benefícios:** Qualidade profissional, menos código boilerplate

#### html2canvas + jsPDF
- **Motivo:** Geração de PDF client-side, sem dependência de servidor
- **Benefícios:** Privacidade dos dados, funcionamento offline

## 4. Funcionalidades Principais

### 4.1 Processo de Geração de Faturas

#### Fluxo Completo
1. **Configuração Inicial:** Número da fatura, datas, logo
2. **Dados das Partes:** Empresa emissora e cliente
3. **Informações de Envio:** Endereços e transportadora (opcional)
4. **Itens da Fatura:** Produtos/serviços com cálculos automáticos
5. **Finalização:** Seleção de template, dados PIX, termos
6. **Geração:** Export para PDF com template escolhido

#### Cálculos Automáticos
- **Subtotal:** Soma de (quantidade × valor unitário) de todos os itens
- **Impostos:** Percentual aplicado sobre subtotal
- **Total Geral:** Subtotal + impostos
- **Atualização:** Tempo real conforme usuário digita

### 4.2 Sistema de Templates

#### Templates Disponíveis
- **Quantidade:** 10 templates únicos
- **Estilos:** Diferentes layouts, cores e organizações
- **Componentes:** Template1.jsx até Template10.jsx
- **Base:** BaseTemplate.jsx para estrutura comum

#### Características dos Templates
- **Responsivos:** Adaptam-se aos dados fornecidos
- **PIX Integration:** QR Code PIX em todos os templates
- **Localização:** Textos em português brasileiro
- **Formatação:** Moeda, datas e números no padrão brasileiro

### 4.3 Integração PIX

#### Funcionalidades PIX
- **QR Code:** Geração automática baseada nos dados
- **Validação:** Verificação de chave PIX, nome e cidade
- **EMV:** Padrão brasileiro para códigos PIX
- **Valor:** Integração automática com total da fatura

#### Implementação
- **Componente:** `PixQRCode.jsx`
- **Utilitário:** `pixQRCode.js`
- **Validação:** `validatePixData()`
- **Geração:** `generatePixQRCode()`

### 4.4 Validações Implementadas

#### Validações de Formulário
- **Campos Obrigatórios:** Nome da empresa, cliente, pelo menos um item
- **Formatos:** Email, telefone, datas
- **Valores:** Números positivos, percentuais válidos
- **PIX:** Chave PIX, nome e cidade do recebedor

#### Validações de Negócio
- **Itens:** Pelo menos um item com valor > 0
- **Datas:** Data de vencimento posterior à data da fatura
- **Cálculos:** Verificação de consistência nos totais

### 4.5 Tratamento de Erros

#### Estratégias de Error Handling
- **Formulários:** Mensagens de erro contextuais
- **PDF Generation:** Fallbacks e mensagens de erro
- **PIX QR Code:** Estados de loading, erro e sucesso
- **LocalStorage:** Recuperação graceful de dados corrompidos

#### Feedback ao Usuário
- **Toast Notifications:** Sonner para notificações
- **Loading States:** Indicadores visuais durante processamento
- **Error Messages:** Mensagens claras em português

### 4.6 Persistência de Dados

#### LocalStorage Strategy
- **Chave:** `formDataV2` para versionamento
- **Dados:** Estado completo do formulário
- **Sincronização:** Auto-save em tempo real
- **Recuperação:** Carregamento automático na inicialização

#### Estrutura dos Dados
```javascript
{
  invoiceData: { number, date, paymentDate, logoUrl },
  businessData: { name, address, phone, email, ... },
  clientData: { name, address, phone, email, ... },
  shippingData: { from, to, transport },
  items: [{ name, description, quantity, rate, amount, tax }],
  taxPercentage: number,
  notes: string,
  termsConditions: array,
  signature: string,
  pixData: { chavePix, nomeRecebedor, cidadeRecebedor },
  selectedCurrency: string,
  selectedTemplate: number
}
```

## 5. Diagramas Detalhados

### 5.1 Diagrama de Componentes

```mermaid
graph TD
    A[App.jsx] --> B[React Router]
    B --> C[Index.jsx]
    B --> D[TemplatePage.jsx]

    C --> E[Tabs Component]
    E --> F[InvoiceDetailsTab]
    E --> G[BusinessClientTab]
    E --> H[ShippingTransportTab]
    E --> I[ItemsTab]
    E --> J[PreviewSummaryTab]

    G --> K[BusinessDetailsModal]
    G --> L[ClientDetailsModal]

    I --> M[ItemDetails]
    M --> N[FloatingLabelInput]

    J --> O[InvoiceTemplate]
    O --> P[Template Registry]
    P --> Q[Template1-10]

    Q --> R[BaseTemplate]
    Q --> S[PixQRCode]

    D --> T[Template Selector]
    D --> U[PDF Generator]
```

### 5.2 Fluxo de Estado

```mermaid
stateDiagram-v2
    [*] --> LoadingData
    LoadingData --> FormFilling
    FormFilling --> InvoiceDetails
    InvoiceDetails --> BusinessClient
    BusinessClient --> Shipping
    Shipping --> Items
    Items --> Preview
    Preview --> TemplateSelection
    TemplateSelection --> PDFGeneration
    PDFGeneration --> [*]

    FormFilling --> FormFilling : Auto-save
    Preview --> Items : Back Navigation
    Items --> Shipping : Back Navigation
    Shipping --> BusinessClient : Back Navigation
    BusinessClient --> InvoiceDetails : Back Navigation
```

### 5.3 Arquitetura de Dados

```mermaid
erDiagram
    INVOICE_DATA {
        string number
        date date
        date paymentDate
        string logoUrl
    }

    BUSINESS_DATA {
        string name
        string address
        string phone
        string email
        string vatNumber
        string displayName
        string country
        string city
        string postalCode
    }

    CLIENT_DATA {
        string name
        string address
        string phone
        string email
        string industry
        string country
        string city
        string postalCode
    }

    SHIPPING_DATA {
        object from
        object to
        object transport
    }

    ITEMS {
        string name
        string description
        number quantity
        number rate
        number amount
        number tax
    }

    PIX_DATA {
        string chavePix
        string nomeRecebedor
        string cidadeRecebedor
    }

    INVOICE_DATA ||--|| BUSINESS_DATA : contains
    INVOICE_DATA ||--|| CLIENT_DATA : contains
    INVOICE_DATA ||--o| SHIPPING_DATA : contains
    INVOICE_DATA ||--o{ ITEMS : contains
    INVOICE_DATA ||--o| PIX_DATA : contains
```

## 6. Componentes Específicos

### 6.1 Componentes de Formulário

#### InvoiceDetailsTab
- **Localização:** `src/components/InvoiceDetailsTab.jsx`
- **Responsabilidade:** Configuração básica da fatura
- **Props:** `invoiceData`, `setInvoiceData`
- **Funcionalidades:**
  - Upload de logo com preview
  - Geração automática de número da fatura
  - Seleção de datas com validação
  - Formatação de campos em tempo real

#### BusinessClientTab
- **Localização:** `src/components/BusinessClientTab.jsx`
- **Responsabilidade:** Gerenciamento de dados de empresa e cliente
- **Props:** `businessData`, `setBusinessData`, `clientData`, `setClientData`
- **Funcionalidades:**
  - Modais para edição detalhada
  - Preview dos dados inseridos
  - Validação de campos obrigatórios
  - Formatação de endereços brasileiros

#### ItemsTab
- **Localização:** `src/components/ItemsTab.jsx`
- **Responsabilidade:** Gerenciamento de itens da fatura
- **Props:** `items`, `setItems`, `selectedCurrency`, `taxPercentage`
- **Funcionalidades:**
  - Adição/remoção dinâmica de itens
  - Cálculos automáticos em tempo real
  - Seleção de moeda
  - Configuração de impostos

### 6.2 Sistema de Templates

#### Template Registry
- **Localização:** `src/utils/templateRegistry.js`
- **Função:** Centralizar registro e acesso aos templates
- **Exports:**
  - `templates`: Array com todos os templates
  - `getTemplate(number)`: Função para obter template específico

#### BaseTemplate
- **Localização:** `src/components/templates/BaseTemplate.jsx`
- **Função:** Estrutura base comum para todos os templates
- **Dimensões:** 794px × 1123px (A4 em pixels)
- **Responsabilidade:** Layout container padrão

#### Templates Individuais
- **Localização:** `src/components/templates/Template[1-10].jsx`
- **Estrutura Comum:**
  - Import do BaseTemplate
  - Formatação de moeda e datas
  - Integração com PixQRCode
  - Dados dinâmicos do formulário

### 6.3 Utilitários Especializados

#### formatCurrency.js
- **Localização:** `src/utils/formatCurrency.js`
- **Funcionalidades:**
  - Formatação multi-moeda (BRL, USD, INR)
  - Locale brasileiro (pt-BR)
  - Extração de símbolos de moeda
  - Configuração de casas decimais

#### invoiceCalculations.js
- **Localização:** `src/utils/invoiceCalculations.js`
- **Funcionalidades:**
  - Cálculo de subtotal
  - Cálculo de impostos
  - Cálculo de total geral
  - Geração de números GST/PAN

#### pixQRCode.js
- **Localização:** `src/utils/pixQRCode.js`
- **Funcionalidades:**
  - Geração de string EMV PIX
  - Cálculo de CRC16
  - Validação de dados PIX
  - Geração de QR Code

#### pdfGenerator.js
- **Localização:** `src/utils/pdfGenerator.js`
- **Funcionalidades:**
  - Renderização de React para HTML
  - Captura via html2canvas
  - Geração de PDF via jsPDF
  - Otimização de qualidade

## 7. Padrões de Desenvolvimento

### 7.1 Convenções de Código

#### Nomenclatura
- **Componentes:** PascalCase (ex: `InvoiceDetailsTab`)
- **Arquivos:** PascalCase para componentes, camelCase para utilitários
- **Variáveis:** camelCase
- **Constantes:** UPPER_SNAKE_CASE

#### Estrutura de Componentes
```javascript
// Imports
import React, { useState, useEffect } from 'react';
import { ComponenteUI } from '@/components/ui/componente';

// Componente principal
const MeuComponente = ({ prop1, prop2 }) => {
  // Estados
  const [estado, setEstado] = useState(valorInicial);

  // Effects
  useEffect(() => {
    // lógica do effect
  }, [dependencias]);

  // Handlers
  const handleAlgumaCoisa = () => {
    // lógica do handler
  };

  // Render
  return (
    <div>
      {/* JSX */}
    </div>
  );
};

export default MeuComponente;
```

### 7.2 Gerenciamento de Estado

#### Estado Local
- **useState:** Para estado de componente simples
- **useEffect:** Para efeitos colaterais e sincronização
- **Props:** Para comunicação entre componentes

#### Estado Global
- **localStorage:** Persistência de dados do formulário
- **Context:** Não utilizado (aplicação simples)
- **Redux:** Não necessário para o escopo atual

### 7.3 Performance

#### Otimizações Implementadas
- **Lazy Loading:** Componentes carregados sob demanda
- **Memoization:** React.memo em componentes pesados
- **Debouncing:** Em cálculos e validações
- **Bundle Splitting:** Via Vite automaticamente

#### Métricas de Performance
- **First Contentful Paint:** < 1.5s
- **Time to Interactive:** < 3s
- **Bundle Size:** < 500KB gzipped

## 8. Segurança e Privacidade

### 8.1 Segurança de Dados

#### Client-Side Only
- **Vantagem:** Dados nunca saem do dispositivo do usuário
- **Implementação:** Processamento 100% no browser
- **Benefício:** Máxima privacidade dos dados sensíveis

#### Validação de Entrada
- **Sanitização:** Limpeza de inputs do usuário
- **Validação:** Zod schemas para validação tipada
- **XSS Prevention:** React escapa automaticamente

### 8.2 Conformidade

#### LGPD (Lei Geral de Proteção de Dados)
- **Coleta Mínima:** Apenas dados necessários para funcionalidade
- **Armazenamento Local:** Dados ficam no dispositivo do usuário
- **Transparência:** Usuário tem controle total dos dados

## 9. Testes e Qualidade

### 9.1 Estratégia de Testes

#### Testes Recomendados
- **Unit Tests:** Utilitários de cálculo e formatação
- **Component Tests:** Componentes de formulário
- **Integration Tests:** Fluxo completo de geração
- **E2E Tests:** Jornada completa do usuário

#### Ferramentas Sugeridas
- **Jest:** Framework de testes
- **React Testing Library:** Testes de componentes
- **Cypress:** Testes end-to-end

### 9.2 Qualidade de Código

#### Linting
- **ESLint:** Configurado para React
- **Prettier:** Formatação automática
- **Husky:** Git hooks para qualidade

#### Code Review
- **Pull Requests:** Revisão obrigatória
- **Automated Checks:** CI/CD com verificações
- **Documentation:** Comentários em código complexo

## 10. Deployment e DevOps

### 10.1 Build Process

#### Vite Build
```bash
npm run build
# Gera pasta dist/ otimizada para produção
```

#### Otimizações de Build
- **Tree Shaking:** Remoção de código não utilizado
- **Code Splitting:** Divisão automática de bundles
- **Asset Optimization:** Compressão de imagens e assets
- **CSS Purging:** Remoção de CSS não utilizado

### 10.2 Deployment Options

#### Opções Recomendadas
- **Netlify:** Deploy automático via Git
- **Vercel:** Otimizado para React/Vite
- **GitHub Pages:** Para projetos open source
- **GPT Engineer:** Deploy integrado

#### Configuração de Deploy
```javascript
// vite.config.js
export default {
  base: '/billify-generator-br/', // Para GitHub Pages
  build: {
    outDir: 'dist',
    sourcemap: false, // Produção
    minify: 'terser'
  }
}
```

---

*Este documento técnico fornece uma visão completa e detalhada da arquitetura do sistema Billify Generator BR, servindo como referência para desenvolvedores que desejam entender, manter ou contribuir com o projeto.*
