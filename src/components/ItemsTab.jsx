import React from 'react';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Plus, Trash2, Image, Settings, Percent } from 'lucide-react';
import { formatCurrency } from '../utils/formatCurrency';

const ItemsTab = ({ 
  items, 
  setItems, 
  selectedCurrency, 
  setSelectedCurrency, 
  taxPercentage, 
  setTaxPercentage 
}) => {
  const handleItemChange = (index, field, value) => {
    const newItems = [...items];
    newItems[index] = { ...newItems[index], [field]: value };
    
    // Recalculate amount when quantity or rate changes
    if (field === 'quantity' || field === 'rate') {
      const quantity = field === 'quantity' ? parseFloat(value) || 0 : newItems[index].quantity;
      const rate = field === 'rate' ? parseFloat(value) || 0 : newItems[index].rate;
      newItems[index].amount = quantity * rate;
    }
    
    setItems(newItems);
  };

  const addItem = () => {
    setItems([...items, { 
      name: "", 
      description: "", 
      quantity: 0, 
      rate: 0, 
      amount: 0, 
      tax: taxPercentage 
    }]);
  };

  const removeItem = (index) => {
    if (items.length > 1) {
      const newItems = items.filter((_, i) => i !== index);
      setItems(newItems);
    }
  };

  const duplicateItem = (index) => {
    const itemToDuplicate = { ...items[index] };
    const newItems = [...items];
    newItems.splice(index + 1, 0, itemToDuplicate);
    setItems(newItems);
  };

  const calculateSubTotal = () => {
    return items.reduce((sum, item) => sum + (item.amount || 0), 0);
  };

  const calculateTaxAmount = () => {
    const subTotal = calculateSubTotal();
    return (subTotal * taxPercentage) / 100;
  };

  const calculateGrandTotal = () => {
    return calculateSubTotal() + calculateTaxAmount();
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Items & Pricing</h2>
          <p className="text-gray-600 mt-1">Add items and configure pricing for your invoice</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Settings className="w-4 h-4 text-gray-500" />
            <Label className="text-sm">Configure TAX</Label>
            <Select value={selectedCurrency} onValueChange={setSelectedCurrency}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="BRL">Brazilian Real (R$)</SelectItem>
                <SelectItem value="USD">US Dollar ($)</SelectItem>
                <SelectItem value="EUR">Euro (€)</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex items-center space-x-2">
            <Label className="text-sm">Number and Currency Format</Label>
            <Label className="text-sm">Edit Columns/Formulas</Label>
          </div>
        </div>
      </div>

      {/* Items Table */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-purple-600 text-white">
                <tr>
                  <th className="px-4 py-3 text-left font-medium">Item</th>
                  <th className="px-4 py-3 text-left font-medium">TAX Rate</th>
                  <th className="px-4 py-3 text-left font-medium">Quantity</th>
                  <th className="px-4 py-3 text-left font-medium">Rate</th>
                  <th className="px-4 py-3 text-left font-medium">Amount</th>
                  <th className="px-4 py-3 text-left font-medium">TAX</th>
                  <th className="px-4 py-3 text-left font-medium">Total</th>
                  <th className="px-4 py-3 w-12"></th>
                </tr>
              </thead>
              <tbody>
                {items.map((item, index) => (
                  <tr key={index} className="border-b hover:bg-gray-50">
                    <td className="px-4 py-4">
                      <div className="space-y-2">
                        <Input
                          value={item.name}
                          onChange={(e) => handleItemChange(index, 'name', e.target.value)}
                          placeholder="Relatório de custos"
                          className="font-medium"
                        />
                        <div className="flex items-center space-x-2">
                          <Button variant="ghost" size="sm" className="text-blue-600 p-0">
                            <Image className="w-4 h-4 mr-1" />
                            Add Description
                          </Button>
                          <Button variant="ghost" size="sm" className="text-blue-600 p-0">
                            <Image className="w-4 h-4 mr-1" />
                            Add Thumbnail
                          </Button>
                          <Button variant="ghost" size="sm" className="text-blue-600 p-0">
                            Duplicate
                          </Button>
                        </div>
                        {item.description && (
                          <Textarea
                            value={item.description}
                            onChange={(e) => handleItemChange(index, 'description', e.target.value)}
                            placeholder="Item description"
                            className="mt-2"
                            rows={2}
                          />
                        )}
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex items-center">
                        <Input
                          type="number"
                          value={item.tax || taxPercentage}
                          onChange={(e) => handleItemChange(index, 'tax', parseFloat(e.target.value) || 0)}
                          className="w-16 text-center"
                          min="0"
                          max="100"
                        />
                        <Percent className="w-4 h-4 ml-1 text-gray-500" />
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <Input
                        type="number"
                        value={item.quantity}
                        onChange={(e) => handleItemChange(index, 'quantity', parseFloat(e.target.value) || 0)}
                        className="w-20 text-center"
                        min="0"
                        step="0.01"
                      />
                    </td>
                    <td className="px-4 py-4">
                      <Input
                        type="number"
                        value={item.rate}
                        onChange={(e) => handleItemChange(index, 'rate', parseFloat(e.target.value) || 0)}
                        className="w-24"
                        min="0"
                        step="0.01"
                      />
                    </td>
                    <td className="px-4 py-4 font-medium">
                      {formatCurrency(item.amount || 0, selectedCurrency)}
                    </td>
                    <td className="px-4 py-4 font-medium">
                      {formatCurrency(((item.amount || 0) * (item.tax || taxPercentage)) / 100, selectedCurrency)}
                    </td>
                    <td className="px-4 py-4 font-bold">
                      {formatCurrency((item.amount || 0) + (((item.amount || 0) * (item.tax || taxPercentage)) / 100), selectedCurrency)}
                    </td>
                    <td className="px-4 py-4">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => removeItem(index)}
                        className="text-red-500 hover:text-red-700"
                        disabled={items.length === 1}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Add New Line Button */}
      <div className="flex justify-between items-center">
        <Button 
          onClick={addItem}
          variant="outline"
          className="text-blue-600 border-blue-200 hover:bg-blue-50"
        >
          <Plus className="w-4 h-4 mr-2" />
          Add New Line
        </Button>
        <Button 
          variant="outline"
          className="text-orange-600 border-orange-200 hover:bg-orange-50"
        >
          <Plus className="w-4 h-4 mr-2" />
          Add New Group
        </Button>
      </div>

      {/* Totals Section */}
      <div className="flex justify-end">
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span>Amount</span>
                <span className="font-medium">{formatCurrency(calculateSubTotal(), selectedCurrency)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>TAX</span>
                <span className="font-medium">{formatCurrency(calculateTaxAmount(), selectedCurrency)}</span>
              </div>
              
              <div className="space-y-2">
                <Button variant="ghost" size="sm" className="text-purple-600 w-full justify-start p-0">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Discounts/Additional Charges
                </Button>
                <Button variant="ghost" size="sm" className="text-purple-600 w-full justify-start p-0">
                  <Plus className="w-4 h-4 mr-2" />
                  Hide Totals
                </Button>
              </div>

              <div className="pt-3 border-t">
                <div className="flex justify-between items-center">
                  <span className="font-semibold text-lg">Total ({selectedCurrency})</span>
                  <span className="font-bold text-xl">{formatCurrency(calculateGrandTotal(), selectedCurrency)}</span>
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  Total (in words): {/* Add number to words conversion here */}
                </div>
              </div>

              <Button variant="ghost" size="sm" className="text-purple-600 w-full justify-start p-0 mt-3">
                <Plus className="w-4 h-4 mr-2" />
                Add More Fields
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ItemsTab;