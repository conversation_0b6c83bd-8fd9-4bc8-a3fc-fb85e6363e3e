import React, { useState } from 'react';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Plus, Edit, ChevronDown, Building2, User } from 'lucide-react';
import BusinessDetailsModal from './BusinessDetailsModal';
import ClientDetailsModal from './ClientDetailsModal';

const BusinessClientTab = ({ 
  businessData, 
  setBusinessData, 
  clientData, 
  setClientData 
}) => {
  const [showBusinessModal, setShowBusinessModal] = useState(false);
  const [showClientModal, setShowClientModal] = useState(false);

  const handleBusinessChange = (field, value) => {
    setBusinessData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleClientChange = (field, value) => {
    setClientData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Business & Client Details</h2>
        <p className="text-gray-600 mt-1">Configure who is sending and receiving this invoice</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* From Section */}
        <Card>
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center text-lg">
              <Building2 className="w-5 h-5 mr-2 text-purple-600" />
              From
              <span className="text-sm font-normal text-gray-500 ml-2">Your Details</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Business Selector */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Business Name*</Label>
              <div className="flex space-x-2">
                <Select value={businessData.name} onValueChange={(value) => handleBusinessChange('name', value)}>
                  <SelectTrigger className="flex-1">
                    <div className="flex items-center space-x-2">
                      <div className="w-6 h-6 bg-purple-100 rounded flex items-center justify-center">
                        <Building2 className="w-3 h-3 text-purple-600" />
                      </div>
                      <SelectValue placeholder="Select a Business" />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="fluxo-services">Fluxo Services</SelectItem>
                    <SelectItem value="tech-solutions">Tech Solutions Ltd</SelectItem>
                    <SelectItem value="digital-agency">Digital Agency Inc</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setShowBusinessModal(true)}
                >
                  <Edit className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Business Details */}
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div>
                  <p className="text-gray-600">Business Name</p>
                  <p className="font-medium">{businessData.name || 'Not set'}</p>
                </div>
                <div>
                  <p className="text-gray-600">Phone</p>
                  <p className="font-medium">{businessData.phone || 'Not set'}</p>
                </div>
              </div>
              <div className="text-sm">
                <p className="text-gray-600">Address</p>
                <p className="font-medium">{businessData.address || 'Not set'}</p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="w-full text-purple-600 hover:text-purple-700 mt-2"
                onClick={() => setShowBusinessModal(true)}
              >
                <Edit className="w-4 h-4 mr-1" />
                Edit Business Details
              </Button>
            </div>

            <Button
              variant="ghost"
              size="sm"
              className="w-full text-purple-600"
              onClick={() => setShowBusinessModal(true)}
            >
              <Plus className="w-4 h-4 mr-2" />
              Add New Business
            </Button>
          </CardContent>
        </Card>

        {/* For Section */}
        <Card>
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center text-lg">
              <User className="w-5 h-5 mr-2 text-blue-600" />
              For
              <span className="text-sm font-normal text-gray-500 ml-2">Client's Details</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Client Selector */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Client Name*</Label>
              <div className="flex space-x-2">
                <Select value={clientData.name} onValueChange={(value) => handleClientChange('name', value)}>
                  <SelectTrigger className="flex-1">
                    <div className="flex items-center space-x-2">
                      <div className="w-6 h-6 bg-blue-100 rounded flex items-center justify-center">
                        <User className="w-3 h-3 text-blue-600" />
                      </div>
                      <SelectValue placeholder="Select a Client" />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="contabilizei">Contabilizei Contabilidade</SelectItem>
                    <SelectItem value="tech-corp">Tech Corp</SelectItem>
                    <SelectItem value="startup-inc">Startup Inc</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setShowClientModal(true)}
                >
                  <Edit className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Client Details */}
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div>
                  <p className="text-gray-600">Business Name</p>
                  <p className="font-medium">{clientData.name || 'Not set'}</p>
                </div>
                <div>
                  <p className="text-gray-600">Industry</p>
                  <p className="font-medium">{clientData.industry || 'Not set'}</p>
                </div>
              </div>
              <div className="text-sm">
                <p className="text-gray-600">Address</p>
                <p className="font-medium">{clientData.address || 'Not set'}</p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="w-full text-blue-600 hover:text-blue-700 mt-2"
                onClick={() => setShowClientModal(true)}
              >
                <Edit className="w-4 h-4 mr-1" />
                Edit Client Details
              </Button>
            </div>

            <Button
              variant="outline"
              size="sm"
              className="w-full text-blue-600 border-blue-200 hover:bg-blue-50"
              onClick={() => setShowClientModal(true)}
            >
              <Plus className="w-4 h-4 mr-2" />
              Add New Client
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Modals */}
      <BusinessDetailsModal
        open={showBusinessModal}
        onOpenChange={setShowBusinessModal}
        businessData={businessData}
        setBusinessData={setBusinessData}
      />

      <ClientDetailsModal
        open={showClientModal}
        onOpenChange={setShowClientModal}
        clientData={clientData}
        setClientData={setClientData}
      />
    </div>
  );
};

export default BusinessClientTab;