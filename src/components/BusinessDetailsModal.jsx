import React, { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Checkbox } from "@/components/ui/checkbox";
import { X, Plus, ChevronUp, ChevronDown } from 'lucide-react';

const BusinessDetailsModal = ({ open, onOpenChange, businessData, setBusinessData }) => {
  const [customFields, setCustomFields] = useState([]);

  const handleInputChange = (field, value) => {
    setBusinessData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = () => {
    onOpenChange(false);
  };

  const addCustomField = () => {
    setCustomFields(prev => [...prev, { label: '', value: '' }]);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="pb-4">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-semibold">Business details</DialogTitle>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onOpenChange(false)}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <Accordion type="single" defaultValue="basic-info" className="w-full">
            <AccordionItem value="basic-info" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-3 hover:no-underline">
                <div className="flex items-center space-x-2">
                  <ChevronDown className="w-4 h-4" />
                  <span className="font-medium">Basic Information</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="businessName">Vendor's Business Name*</Label>
                      <Input
                        id="businessName"
                        value={businessData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        placeholder="Fluxo Services"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="city">City/Town</Label>
                      <Input
                        id="city"
                        value={businessData.city}
                        onChange={(e) => handleInputChange('city', e.target.value)}
                        placeholder="Franca"
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="country">Select Country*</Label>
                    <Select value={businessData.country} onValueChange={(value) => handleInputChange('country', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Brazil" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Brazil">Brazil</SelectItem>
                        <SelectItem value="USA">United States</SelectItem>
                        <SelectItem value="UK">United Kingdom</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>

          {/* Tax Information */}
          <Accordion type="single" className="w-full">
            <AccordionItem value="tax-info" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-3 hover:no-underline">
                <div className="flex items-center space-x-2">
                  <ChevronDown className="w-4 h-4" />
                  <span className="font-medium">Tax Information</span>
                  <span className="text-sm text-gray-500">(optional)</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="vatNumber">VAT Number</Label>
                    <Input
                      id="vatNumber"
                      value={businessData.vatNumber}
                      onChange={(e) => handleInputChange('vatNumber', e.target.value)}
                      placeholder="18"
                    />
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>

          {/* Address */}
          <Accordion type="single" className="w-full">
            <AccordionItem value="address" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-3 hover:no-underline">
                <div className="flex items-center space-x-2">
                  <ChevronDown className="w-4 h-4" />
                  <span className="font-medium">Address</span>
                  <span className="text-sm text-gray-500">(optional)</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="addressCountry">Select Country</Label>
                      <Select value={businessData.country} onValueChange={(value) => handleInputChange('country', value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Brazil" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Brazil">Brazil</SelectItem>
                          <SelectItem value="USA">United States</SelectItem>
                          <SelectItem value="UK">United Kingdom</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="state">State / Province</Label>
                      <Input
                        id="state"
                        value={businessData.state}
                        onChange={(e) => handleInputChange('state', e.target.value)}
                        placeholder="São Paulo"
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="addressCity">City/Town</Label>
                      <Input
                        id="addressCity"
                        value={businessData.city}
                        onChange={(e) => handleInputChange('city', e.target.value)}
                        placeholder="Franca"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="postalCode">Postal Code / Zip Code</Label>
                      <Input
                        id="postalCode"
                        value={businessData.postalCode}
                        onChange={(e) => handleInputChange('postalCode', e.target.value)}
                        placeholder="Postal Code / Zip Code"
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="streetAddress">Street Address</Label>
                    <Input
                      id="streetAddress"
                      value={businessData.address}
                      onChange={(e) => handleInputChange('address', e.target.value)}
                      placeholder="R. Cel Tamarindo"
                    />
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>

          {/* Additional Details */}
          <Accordion type="single" className="w-full">
            <AccordionItem value="additional" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-3 hover:no-underline">
                <div className="flex items-center space-x-2">
                  <ChevronDown className="w-4 h-4" />
                  <span className="font-medium">Additional Details</span>
                  <span className="text-sm text-gray-500">(optional)</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="displayName">Display Name</Label>
                    <Input
                      id="displayName"
                      value={businessData.displayName}
                      onChange={(e) => handleInputChange('displayName', e.target.value)}
                      placeholder="Fluxo Sistemas"
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <p className="text-sm text-gray-500">Add to directly email documents from Refrens</p>
                      <Input
                        id="email"
                        type="email"
                        value={businessData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        placeholder="Email"
                      />
                      <div className="flex items-center space-x-2">
                        <Checkbox id="showEmailInInvoice" />
                        <Label htmlFor="showEmailInInvoice" className="text-sm">Show Email in Invoice</Label>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone No.</Label>
                      <p className="text-sm text-gray-500">Add to directly WhatsApp documents from Refrens</p>
                      <div className="flex">
                        <Select defaultValue="+55">
                          <SelectTrigger className="w-20">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="+55">🇧🇷 +55</SelectItem>
                            <SelectItem value="+1">🇺🇸 +1</SelectItem>
                            <SelectItem value="+44">🇬🇧 +44</SelectItem>
                          </SelectContent>
                        </Select>
                        <Input
                          className="rounded-l-none"
                          value={businessData.phone}
                          onChange={(e) => handleInputChange('phone', e.target.value)}
                          placeholder="+55"
                        />
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="showPhoneInInvoice" defaultChecked />
                        <Label htmlFor="showPhoneInInvoice" className="text-sm">Show Phone in Invoice</Label>
                      </div>
                    </div>
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-purple-600"
                    onClick={addCustomField}
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Add Custom Fields
                  </Button>
                  
                  <div className="flex items-center space-x-2">
                    <Checkbox id="updateChanges" />
                    <Label htmlFor="updateChanges" className="text-sm">
                      Update changes for Previous and Future documents.
                    </Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Checkbox id="onlyFutureUpdates" defaultChecked />
                    <Label htmlFor="onlyFutureUpdates" className="text-sm">
                      Only update for Future documents
                    </Label>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>

        <div className="flex justify-end pt-6 border-t">
          <Button onClick={handleSave} className="bg-purple-600 hover:bg-purple-700">
            Save
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default BusinessDetailsModal;